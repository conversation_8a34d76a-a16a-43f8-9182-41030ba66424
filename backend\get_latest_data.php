<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Set timezone to India
date_default_timezone_set('Asia/Kolkata');

// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "esp32_data";

try {
    // Create connection
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    // Get parameters
    $records = isset($_GET['records']) ? intval($_GET['records']) : 1;
    $records = max(1, min($records, 1000)); // Limit between 1 and 1000 records
    
    // Prepare SQL statement
    $sql = "SELECT * FROM electrical_data ORDER BY timestamp DESC LIMIT ?";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param("i", $records);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        // Convert timestamp to IST and format data
        $timestamp = new DateTime($row["timestamp"]);
        $timestamp->setTimezone(new DateTimeZone('Asia/Kolkata'));
        $timestamp_ist = $timestamp->format('Y-m-d H:i:s');
        
        $data[] = [
            "id" => intval($row["id"]),
            "voltage_1" => floatval($row["voltage_1"]),
            "voltage_2" => floatval($row["voltage_2"]),
            "voltage_3" => floatval($row["voltage_3"]),
            "current_1" => floatval($row["current_1"]),
            "current_2" => floatval($row["current_2"]),
            "current_3" => floatval($row["current_3"]),
            "pf_1" => floatval($row["pf_1"]),
            "pf_2" => floatval($row["pf_2"]),
            "pf_3" => floatval($row["pf_3"]),
            "kva_1" => floatval($row["kva_1"]),
            "kva_2" => floatval($row["kva_2"]),
            "kva_3" => floatval($row["kva_3"]),
            "total_kva" => floatval($row["total_kva"]),
            "total_kw" => floatval($row["total_kw"]),
            "total_kvar" => floatval($row["total_kvar"]),
            "frequency" => floatval($row["frequency"]),
            "timestamp" => $timestamp_ist
        ];
    }
    
    $stmt->close();
    
    // If only one record requested, return single object instead of array
    if ($records == 1 && count($data) > 0) {
        echo json_encode($data[0]);
    } else {
        echo json_encode($data);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s', time())
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
