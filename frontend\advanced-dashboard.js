/**
 * Advanced Power Monitoring Dashboard
 * Main JavaScript file for the advanced dashboard interface
 */

// Global chart variables
let voltageChart, currentChart, pfChart, kvaChart, totalPowerChart, frequencyChart;
let dataUpdateInterval;
let lastUpdateTime = null;
let isPaused = false;
let darkMode = false;

// Color schemes for charts
const colorSchemes = {
    professional: {
        primary: [
            '#2563eb', // Blue
            '#dc2626', // Red
            '#16a34a', // Green
            '#ea580c', // Orange
            '#7c3aed', // Purple
            '#0891b2'  // Cyan
        ],
        secondary: [
            '#3b82f6',
            '#ef4444',
            '#22c55e',
            '#f97316',
            '#8b5cf6',
            '#06b6d4'
        ]
    },
    modern: {
        primary: [
            '#6366f1', // Indigo
            '#f59e0b', // Amber
            '#10b981', // Emerald
            '#ef4444', // Red
            '#8b5cf6', // Violet
            '#06b6d4'  // Cy<PERSON>
        ]
    },
    classic: {
        primary: [
            '#1f2937', // Gray
            '#991b1b', // Dark Red
            '#166534', // Dark Green
            '#92400e', // Dark Orange
            '#581c87', // Dark Purple
            '#155e75'  // Dark Cyan
        ]
    }
};

// Dashboard settings with defaults
const dashboardSettings = {
    refreshRate: 2, // seconds
    timeWindow: 1, // minutes
    decimalPlaces: 3,
    chartTheme: 'professional',
    lineThickness: 0.5,
    showGridLines: true,
    voltageAlertHigh: 450,
    voltageAlertLow: 350,
    frequencyAlertHigh: 55,
    frequencyAlertLow: 45
};

// Initialize the dashboard when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing Advanced Power Monitoring Dashboard');

    // Initialize charts
    initializeCharts();

    // Set up event listeners
    setupEventListeners();

    // Start data updates
    startDataUpdates();

    // Check for dark mode preference
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        toggleDarkMode();
    }

    // Show initial notification
    showNotification('Dashboard initialized', 'info');
});

/**
 * Initialize all charts on the dashboard
 */
function initializeCharts() {
    console.log('Initializing charts with zero-value support...');

    // Voltage Chart - Set range to show zero values clearly
    voltageChart = createProfessionalChart(
        'voltageChart',
        ['Phase 1', 'Phase 2', 'Phase 3'],
        colorSchemes.professional.primary,
        'Voltage (V)',
        {
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 300,
                    suggestedMin: 0,
                    suggestedMax: 300
                }
            }
        },
        'voltage'
    );

    // Current Chart - Set range to show zero values clearly
    currentChart = createProfessionalChart(
        'currentChart',
        ['Phase 1', 'Phase 2', 'Phase 3'],
        colorSchemes.professional.primary,
        'Current (A)',
        {
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 25,
                    suggestedMin: 0,
                    suggestedMax: 25
                }
            }
        },
        'current'
    );

    // Power Factor Chart - Set range for PF values including zero
    pfChart = createProfessionalChart(
        'pfChart',
        ['Phase 1', 'Phase 2', 'Phase 3'],
        colorSchemes.professional.primary,
        'Power Factor',
        {
            scales: {
                y: {
                    beginAtZero: true,
                    min: -0.3,
                    max: 1.0,
                    suggestedMin: -0.3,
                    suggestedMax: 1.0
                }
            }
        },
        'pf'
    );

    // KVA Chart - Set range to show zero values clearly
    kvaChart = createProfessionalChart(
        'kvaChart',
        ['Phase 1', 'Phase 2', 'Phase 3'],
        colorSchemes.professional.primary,
        'KVA',
        {
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 2000,
                    suggestedMin: 0,
                    suggestedMax: 2000
                }
            }
        },
        'kva'
    );

    // Total Power Chart - Set range to show zero values clearly
    totalPowerChart = createProfessionalChart(
        'totalPowerChart',
        ['Total KW', 'Total KVA', 'Total KVAR'],
        colorSchemes.professional.primary,
        'Power',
        {
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 5000,
                    suggestedMin: 0,
                    suggestedMax: 5000
                }
            }
        },
        'totalPower'
    );

    // Frequency Chart - Set range for frequency including zero
    frequencyChart = createProfessionalChart(
        'frequencyChart',
        ['Frequency'],
        [colorSchemes.professional.primary[0]],
        'Frequency (Hz)',
        {
            scales: {
                y: {
                    beginAtZero: true,
                    min: 30,
                    max: 60,
                    suggestedMin: 30,
                    suggestedMax: 60
                }
            }
        },
        'frequency'
    );

    console.log('All charts initialized with zero-value support');
}

/**
 * Set up all event listeners for the dashboard
 */
function setupEventListeners() {
    // Theme toggle
    document.getElementById('themeToggle').addEventListener('click', toggleDarkMode);

    // Settings link
    document.getElementById('settingsLink').addEventListener('click', function(e) {
        e.preventDefault();
        document.getElementById('settingsModal').classList.add('active');
    });

    // Export data button
    document.getElementById('exportDataBtn').addEventListener('click', function() {
        document.getElementById('exportModal').classList.add('active');
    });

    // Close modal buttons
    document.querySelectorAll('.close-modal').forEach(button => {
        button.addEventListener('click', function() {
            this.closest('.modal').classList.remove('active');
        });
    });

    // Save settings button
    document.getElementById('saveSettings').addEventListener('click', saveSettings);

    // Reset settings button
    document.getElementById('resetSettings').addEventListener('click', resetSettings);

    // Auto range button
    document.getElementById('autoRangeBtn').addEventListener('click', applyAutoRange);

    // Export type change
    document.getElementById('exportTimeRange').addEventListener('change', function() {
        const customRange = document.getElementById('customDateRange');
        if (this.value === 'custom') {
            customRange.style.display = 'block';
        } else {
            customRange.style.display = 'none';
        }
    });

    // Confirm export button
    document.getElementById('confirmExport').addEventListener('click', exportData);

    // Cancel export button
    document.getElementById('cancelExport').addEventListener('click', function() {
        document.getElementById('exportModal').classList.remove('active');
    });

    // Fullscreen close button
    document.getElementById('fullscreenClose').addEventListener('click', closeFullscreen);

    // Time navigation buttons
    document.getElementById('prevHourBtn').addEventListener('click', navigateToPreviousHour);
    document.getElementById('nextHourBtn').addEventListener('click', navigateToNextHour);

    // Show all data button
    document.getElementById('showAllDataBtn').addEventListener('click', function() {
        window.location.href = 'history.php';
    });
}

/**
 * Start periodic data updates
 */
function startDataUpdates() {
    // Fetch data immediately
    fetchDataAndUpdateCharts();

    // Set up interval for regular updates
    dataUpdateInterval = setInterval(fetchDataAndUpdateCharts, dashboardSettings.refreshRate * 1000);

    console.log(`Data updates started with refresh rate of ${dashboardSettings.refreshRate} seconds`);
}

/**
 * Fetch latest data and update all charts
 */
async function fetchDataAndUpdateCharts() {
    if (isPaused) return;

    try {
        // Add cache-busting parameter
        const timestamp = new Date().getTime();
        const apiUrl = `../backend/get_latest_data.php?_=${timestamp}&records=20`;

        console.log('Fetching data from:', apiUrl);

        const response = await fetch(apiUrl);

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status} - ${response.statusText}`);
        }

        const rawData = await response.text();
        console.log('Raw API response:', rawData);

        let data;
        try {
            data = JSON.parse(rawData);
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            throw new Error('Invalid JSON response from server');
        }

        console.log('Parsed data:', data);

        // Check if we have valid data
        if (!data) {
            throw new Error('No data received from server');
        }

        // Handle error responses from the API
        if (data.error) {
            throw new Error(`API Error: ${data.error}`);
        }

        // If we got an array of records, use the most recent one for summary
        const latestData = Array.isArray(data) ? data[0] : data;

        if (!latestData) {
            throw new Error('No valid data points received');
        }

        console.log('Latest data point:', latestData);

        // Update timestamp display
        updateLastUpdatedTime(latestData.timestamp);

        // Update summary cards
        updateSummaryCards(latestData);

        // Update charts with all received data points
        if (Array.isArray(data)) {
            console.log(`Processing ${data.length} data points for charts`);
            // Process data in reverse order to add oldest points first
            for (let i = data.length - 1; i >= 0; i--) {
                updateChartsWithData(data[i]);
            }
        } else {
            console.log('Processing single data point for charts');
            updateChartsWithData(latestData);
        }

        // Update status indicator
        updateStatus('Data updated successfully', 'success');

        // Log successful update
        console.log('Dashboard updated successfully at:', new Date().toLocaleTimeString());

    } catch (error) {
        console.error('Error fetching data:', error);
        updateStatus(`Failed to fetch data: ${error.message}`, 'error');

        // Show detailed error in console for debugging
        console.error('Detailed error information:', {
            message: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString()
        });
    }
}

/**
 * Update charts with data from a single data point
 * @param {Object} data - The data point to add to charts
 */
function updateChartsWithData(data) {
    if (!data || !data.timestamp) {
        console.warn('Invalid data received:', data);
        return;
    }

    // Parse timestamp
    const timestamp = new Date(data.timestamp);

    // Debug: Log the data being processed
    console.log('Processing data point:', {
        timestamp: timestamp.toISOString(),
        voltage_1: data.voltage_1,
        current_1: data.current_1,
        frequency: data.frequency
    });

    // Helper function to safely parse float values (including zeros)
    const safeParseFloat = (value) => {
        const parsed = parseFloat(value);
        return isNaN(parsed) ? 0 : parsed; // Default to 0 if NaN
    };

    // Update voltage chart - ALWAYS plot values, even if zero
    const voltageValues = [
        safeParseFloat(data.voltage_1),
        safeParseFloat(data.voltage_2),
        safeParseFloat(data.voltage_3)
    ];
    console.log('Voltage values:', voltageValues);
    updateProfessionalChart(voltageChart, timestamp, voltageValues, true);

    // Update current chart - ALWAYS plot values, even if zero
    const currentValues = [
        safeParseFloat(data.current_1),
        safeParseFloat(data.current_2),
        safeParseFloat(data.current_3)
    ];
    console.log('Current values:', currentValues);
    updateProfessionalChart(currentChart, timestamp, currentValues, true);

    // Update power factor chart - ALWAYS plot values, even if zero
    const pfValues = [
        safeParseFloat(data.pf_1),
        safeParseFloat(data.pf_2),
        safeParseFloat(data.pf_3)
    ];
    console.log('Power Factor values:', pfValues);
    updateProfessionalChart(pfChart, timestamp, pfValues, true);

    // Update KVA chart - ALWAYS plot values, even if zero
    const kvaValues = [
        safeParseFloat(data.kva_1),
        safeParseFloat(data.kva_2),
        safeParseFloat(data.kva_3)
    ];
    console.log('KVA values:', kvaValues);
    updateProfessionalChart(kvaChart, timestamp, kvaValues, true);

    // Update total power chart - ALWAYS plot values, even if zero
    const totalPowerValues = [
        safeParseFloat(data.total_kw),
        safeParseFloat(data.total_kva),
        safeParseFloat(data.total_kvar)
    ];
    console.log('Total Power values:', totalPowerValues);
    updateProfessionalChart(totalPowerChart, timestamp, totalPowerValues, true);

    // Update frequency chart - ALWAYS plot values, even if zero
    const frequencyValue = safeParseFloat(data.frequency);
    console.log('Frequency value:', frequencyValue);
    updateProfessionalChart(frequencyChart, timestamp, [frequencyValue], true);

    // Update instant values display
    updateInstantValues(data);

    // Log successful chart update
    console.log('Charts updated successfully at:', timestamp.toLocaleTimeString());
}

/**
 * Update the summary cards with latest data
 * @param {Object} data - The latest data point
 */
function updateSummaryCards(data) {
    if (!data) return;

    // Calculate average voltage
    const avgVoltage = ((parseFloat(data.voltage_1) + parseFloat(data.voltage_2) + parseFloat(data.voltage_3)) / 3).toFixed(dashboardSettings.decimalPlaces);
    document.getElementById('avgVoltage').textContent = `${avgVoltage} V`;

    // Calculate total current
    const totalCurrent = (parseFloat(data.current_1) + parseFloat(data.current_2) + parseFloat(data.current_3)).toFixed(dashboardSettings.decimalPlaces);
    document.getElementById('totalCurrent').textContent = `${totalCurrent} A`;

    // Total power
    document.getElementById('totalPower').textContent = `${parseFloat(data.total_kw).toFixed(dashboardSettings.decimalPlaces)} kW`;

    // Frequency
    document.getElementById('freqValue').textContent = `${parseFloat(data.frequency).toFixed(dashboardSettings.decimalPlaces)} Hz`;
}

/**
 * Update the instant values display for each chart
 * @param {Object} data - The latest data point
 */
function updateInstantValues(data) {
    if (!data) return;

    // Format values with specified decimal places
    const formatValue = (value) => parseFloat(value).toFixed(dashboardSettings.decimalPlaces);

    // Voltage values
    document.getElementById('voltageValues').innerHTML = `
        <div>Phase 1: ${formatValue(data.voltage_1)} V</div>
        <div>Phase 2: ${formatValue(data.voltage_2)} V</div>
        <div>Phase 3: ${formatValue(data.voltage_3)} V</div>
    `;

    // Current values
    document.getElementById('currentValues').innerHTML = `
        <div>Phase 1: ${formatValue(data.current_1)} A</div>
        <div>Phase 2: ${formatValue(data.current_2)} A</div>
        <div>Phase 3: ${formatValue(data.current_3)} A</div>
    `;

    // Power factor values
    document.getElementById('pfValues').innerHTML = `
        <div>Phase 1: ${formatValue(data.pf_1)}</div>
        <div>Phase 2: ${formatValue(data.pf_2)}</div>
        <div>Phase 3: ${formatValue(data.pf_3)}</div>
    `;

    // KVA values
    document.getElementById('kvaValues').innerHTML = `
        <div>Phase 1: ${formatValue(data.kva_1)} kVA</div>
        <div>Phase 2: ${formatValue(data.kva_2)} kVA</div>
        <div>Phase 3: ${formatValue(data.kva_3)} kVA</div>
    `;

    // Total power values
    document.getElementById('totalPowerValues').innerHTML = `
        <div>Total KW: ${formatValue(data.total_kw)} kW</div>
        <div>Total KVA: ${formatValue(data.total_kva)} kVA</div>
        <div>Total KVAR: ${formatValue(data.total_kvar)} kVAR</div>
    `;

    // Frequency value
    document.getElementById('frequencyValue').innerHTML = `
        <div>Frequency: ${formatValue(data.frequency)} Hz</div>
    `;
}

/**
 * Update the last updated time display
 * @param {string} timestamp - The timestamp string
 */
function updateLastUpdatedTime(timestamp) {
    if (!timestamp) return;

    // Parse the timestamp
    const date = new Date(timestamp);

    // Format the time in IST
    const formattedTime = date.toLocaleTimeString('en-IN', {
        timeZone: 'Asia/Kolkata',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        day: '2-digit',
        month: 'short'
    });

    // Update the display
    document.getElementById('lastUpdatedTime').textContent = formattedTime;
}

/**
 * Toggle dark mode for the dashboard
 */
function toggleDarkMode() {
    const body = document.body;
    const themeToggle = document.getElementById('themeToggle');
    const themeIcon = themeToggle.querySelector('.material-icons-round');
    const themeText = themeToggle.querySelector('span:not(.material-icons-round)');

    // Toggle dark mode class
    body.classList.toggle('dark-theme');
    body.classList.toggle('light-theme');

    // Update dark mode state
    darkMode = body.classList.contains('dark-theme');

    // Update button text and icon
    if (darkMode) {
        themeIcon.textContent = 'light_mode';
        themeText.textContent = 'Light Mode';
    } else {
        themeIcon.textContent = 'dark_mode';
        themeText.textContent = 'Dark Mode';
    }

    // Update chart colors based on theme
    updateChartColors();
}

/**
 * Update chart colors based on current theme
 */
function updateChartColors() {
    // Update chart colors based on theme
    const charts = [voltageChart, currentChart, pfChart, kvaChart, totalPowerChart, frequencyChart];

    charts.forEach(chart => {
        if (!chart) return;

        // Update grid colors
        chart.options.scales.x.grid.color = darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.03)';
        chart.options.scales.y.grid.color = darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.03)';

        // Update text colors
        chart.options.scales.x.ticks.color = darkMode ? 'rgba(255, 255, 255, 0.7)' : '#495057';
        chart.options.scales.y.ticks.color = darkMode ? 'rgba(255, 255, 255, 0.7)' : '#495057';

        // Update tooltip colors
        chart.options.plugins.tooltip.backgroundColor = darkMode ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.9)';
        chart.options.plugins.tooltip.titleColor = darkMode ? '#ffffff' : '#202124';
        chart.options.plugins.tooltip.bodyColor = darkMode ? 'rgba(255, 255, 255, 0.8)' : '#5f6368';
        chart.options.plugins.tooltip.borderColor = darkMode ? 'rgba(255, 255, 255, 0.2)' : '#dadce0';

        // Update crosshair color
        if (chart.options.plugins.crosshair) {
            chart.options.plugins.crosshair.line.color = darkMode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)';
        }

        // Update the chart
        chart.update();
    });
}

/**
 * Save dashboard settings from the settings modal
 */
function saveSettings() {
    // Get values from form
    dashboardSettings.refreshRate = parseInt(document.getElementById('refreshRate').value) || 2;
    dashboardSettings.timeWindow = parseInt(document.getElementById('timeWindow').value) || 1;
    dashboardSettings.decimalPlaces = parseInt(document.getElementById('decimalPlaces').value) || 3;
    dashboardSettings.chartTheme = document.getElementById('chartTheme').value;
    dashboardSettings.lineThickness = parseFloat(document.getElementById('lineThickness').value) || 0.5;
    dashboardSettings.showGridLines = document.getElementById('showGridLines').checked;
    dashboardSettings.voltageAlertHigh = parseInt(document.getElementById('voltageAlertHigh').value) || 450;
    dashboardSettings.voltageAlertLow = parseInt(document.getElementById('voltageAlertLow').value) || 350;
    dashboardSettings.frequencyAlertHigh = parseInt(document.getElementById('frequencyAlertHigh').value) || 55;
    dashboardSettings.frequencyAlertLow = parseInt(document.getElementById('frequencyAlertLow').value) || 45;

    // Apply settings
    applySettings();

    // Close modal
    document.getElementById('settingsModal').classList.remove('active');

    // Show notification
    showNotification('Settings saved successfully', 'success');
}

/**
 * Reset dashboard settings to defaults
 */
function resetSettings() {
    // Reset to defaults
    dashboardSettings.refreshRate = 2;
    dashboardSettings.timeWindow = 1;
    dashboardSettings.decimalPlaces = 3;
    dashboardSettings.chartTheme = 'professional';
    dashboardSettings.lineThickness = 0.5;
    dashboardSettings.showGridLines = true;
    dashboardSettings.voltageAlertHigh = 450;
    dashboardSettings.voltageAlertLow = 350;
    dashboardSettings.frequencyAlertHigh = 55;
    dashboardSettings.frequencyAlertLow = 45;

    // Update form values
    document.getElementById('refreshRate').value = dashboardSettings.refreshRate;
    document.getElementById('timeWindow').value = dashboardSettings.timeWindow;
    document.getElementById('decimalPlaces').value = dashboardSettings.decimalPlaces;
    document.getElementById('chartTheme').value = dashboardSettings.chartTheme;
    document.getElementById('lineThickness').value = dashboardSettings.lineThickness;
    document.getElementById('showGridLines').checked = dashboardSettings.showGridLines;
    document.getElementById('voltageAlertHigh').value = dashboardSettings.voltageAlertHigh;
    document.getElementById('voltageAlertLow').value = dashboardSettings.voltageAlertLow;
    document.getElementById('frequencyAlertHigh').value = dashboardSettings.frequencyAlertHigh;
    document.getElementById('frequencyAlertLow').value = dashboardSettings.frequencyAlertLow;

    // Apply settings
    applySettings();

    // Show notification
    showNotification('Settings reset to defaults', 'info');
}

/**
 * Apply current settings to the dashboard
 */
function applySettings() {
    // Update refresh interval
    clearInterval(dataUpdateInterval);
    dataUpdateInterval = setInterval(fetchDataAndUpdateCharts, dashboardSettings.refreshRate * 1000);

    // Update chart line thickness
    const charts = [voltageChart, currentChart, pfChart, kvaChart, totalPowerChart, frequencyChart];
    charts.forEach(chart => {
        if (!chart) return;

        // Update line thickness
        chart.data.datasets.forEach(dataset => {
            dataset.borderWidth = dashboardSettings.lineThickness;
        });

        // Update grid visibility
        chart.options.scales.x.grid.display = dashboardSettings.showGridLines;
        chart.options.scales.y.grid.display = dashboardSettings.showGridLines;

        // Update the chart
        chart.update();
    });

    // Update chart theme
    updateChartTheme(dashboardSettings.chartTheme);

    // Update all displayed values to use new decimal places
    if (lastUpdateTime) {
        updateInstantValues(lastUpdateTime);
    }
}

/**
 * Update chart theme based on selected theme
 * @param {string} theme - The theme name
 */
function updateChartTheme(theme) {
    const colors = colorSchemes[theme] ? colorSchemes[theme].primary : colorSchemes.professional.primary;

    // Update chart colors
    const charts = [
        { chart: voltageChart, labels: ['Phase 1', 'Phase 2', 'Phase 3'] },
        { chart: currentChart, labels: ['Phase 1', 'Phase 2', 'Phase 3'] },
        { chart: pfChart, labels: ['Phase 1', 'Phase 2', 'Phase 3'] },
        { chart: kvaChart, labels: ['Phase 1', 'Phase 2', 'Phase 3'] },
        { chart: totalPowerChart, labels: ['Total KW', 'Total KVA', 'Total KVAR'] },
        { chart: frequencyChart, labels: ['Frequency'] }
    ];

    charts.forEach(({ chart, labels }) => {
        if (!chart) return;

        chart.data.datasets.forEach((dataset, index) => {
            dataset.borderColor = colors[index % colors.length];
            dataset.pointHoverBackgroundColor = colors[index % colors.length];
        });

        chart.update();
    });
}

/**
 * Apply auto range to all charts
 */
function applyAutoRange() {
    const charts = [voltageChart, currentChart, pfChart, kvaChart, totalPowerChart, frequencyChart];

    charts.forEach(chart => {
        if (!chart) return;

        // Get the chart's parameter type
        const parameterType = chart._parameterType;

        // For PF and frequency charts, use fixed ranges
        if (parameterType === 'pf') {
            chart.options.scales.y.min = -0.3;
            chart.options.scales.y.max = 1;
        } else if (parameterType === 'frequency') {
            chart.options.scales.y.min = 30;
            chart.options.scales.y.max = 60;
        } else {
            // For other charts, calculate min/max from data
            let dataMin = Infinity;
            let dataMax = -Infinity;

            chart.data.datasets.forEach(dataset => {
                dataset.data.forEach(point => {
                    dataMin = Math.min(dataMin, point.y);
                    dataMax = Math.max(dataMax, point.y);
                });
            });

            // Add padding for better visualization
            const padding = (dataMax - dataMin) * 0.25; // 25% padding

            // Ensure min is never negative for values that should be positive
            if (parameterType === 'voltage' || parameterType === 'current' ||
                parameterType === 'kva' || parameterType === 'totalPower') {
                dataMin = Math.max(0, dataMin - padding);
            } else {
                dataMin = dataMin - padding;
            }

            dataMax = dataMax + padding;

            // Apply calculated min/max
            chart.options.scales.y.min = dataMin;
            chart.options.scales.y.max = dataMax;
        }

        // Update the chart
        chart.update();
    });

    // Show notification
    showNotification('Auto range applied to all charts', 'success');
}

/**
 * Export data based on selected options
 */
function exportData() {
    // Get export options
    const exportType = document.querySelector('input[name="exportType"]:checked').value;
    const timeRange = document.getElementById('exportTimeRange').value;
    const includeAllParameters = document.getElementById('includeAllParameters').checked;

    // Calculate date range
    let startDate, endDate;

    switch (timeRange) {
        case 'visible':
            // Use currently visible data range
            startDate = voltageChart.options.scales.x.min;
            endDate = voltageChart.options.scales.x.max;
            break;
        case 'hour':
            endDate = new Date();
            startDate = new Date(endDate.getTime() - (60 * 60 * 1000)); // 1 hour ago
            break;
        case 'day':
            endDate = new Date();
            startDate = new Date(endDate.getTime() - (24 * 60 * 60 * 1000)); // 24 hours ago
            break;
        case 'week':
            endDate = new Date();
            startDate = new Date(endDate.getTime() - (7 * 24 * 60 * 60 * 1000)); // 7 days ago
            break;
        case 'custom':
            startDate = new Date(document.getElementById('exportStartDate').value);
            endDate = new Date(document.getElementById('exportEndDate').value);
            break;
        default:
            endDate = new Date();
            startDate = new Date(endDate.getTime() - (60 * 60 * 1000)); // Default to 1 hour
    }

    // Format dates for API
    const startDateStr = startDate.toISOString();
    const endDateStr = endDate.toISOString();

    // Show loading notification
    showNotification('Preparing data for export...', 'info');

    // Fetch data for export
    fetch(`../backend/get_historical_data.php?start=${startDateStr}&end=${endDateStr}&limit=100000`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }

            // Process data based on export type
            switch (exportType) {
                case 'csv':
                    exportAsCSV(data, includeAllParameters);
                    break;
                case 'json':
                    exportAsJSON(data, includeAllParameters);
                    break;
                case 'pdf':
                    showNotification('PDF export is not yet implemented', 'warning');
                    break;
                default:
                    exportAsCSV(data, includeAllParameters);
            }

            // Close modal
            document.getElementById('exportModal').classList.remove('active');
        })
        .catch(error => {
            console.error('Error exporting data:', error);
            showNotification('Failed to export data: ' + error.message, 'error');
        });
}

/**
 * Export data as CSV file
 * @param {Array} data - The data to export
 * @param {boolean} includeAllParameters - Whether to include all parameters
 */
function exportAsCSV(data, includeAllParameters) {
    if (!data || !Array.isArray(data)) {
        showNotification('No data to export', 'warning');
        return;
    }

    // Define headers based on includeAllParameters
    let headers = ['timestamp'];
    let rows = [];

    if (includeAllParameters) {
        headers = headers.concat([
            'voltage_1', 'voltage_2', 'voltage_3',
            'current_1', 'current_2', 'current_3',
            'pf_1', 'pf_2', 'pf_3',
            'kva_1', 'kva_2', 'kva_3',
            'total_kw', 'total_kva', 'total_kvar',
            'frequency'
        ]);

        // Create rows
        rows = data.map(item => {
            return [
                item.timestamp,
                item.voltage_1, item.voltage_2, item.voltage_3,
                item.current_1, item.current_2, item.current_3,
                item.pf_1, item.pf_2, item.pf_3,
                item.kva_1, item.kva_2, item.kva_3,
                item.total_kw, item.total_kva, item.total_kvar,
                item.frequency
            ].join(',');
        });
    } else {
        // Only include essential parameters
        headers = headers.concat([
            'voltage_avg', 'current_total', 'pf_avg',
            'total_kw', 'total_kva', 'frequency'
        ]);

        // Create rows with calculated values
        rows = data.map(item => {
            const voltageAvg = ((parseFloat(item.voltage_1) + parseFloat(item.voltage_2) + parseFloat(item.voltage_3)) / 3).toFixed(3);
            const currentTotal = (parseFloat(item.current_1) + parseFloat(item.current_2) + parseFloat(item.current_3)).toFixed(3);
            const pfAvg = ((parseFloat(item.pf_1) + parseFloat(item.pf_2) + parseFloat(item.pf_3)) / 3).toFixed(3);

            return [
                item.timestamp,
                voltageAvg, currentTotal, pfAvg,
                item.total_kw, item.total_kva, item.frequency
            ].join(',');
        });
    }

    // Create CSV content
    const csvContent = [headers.join(',')].concat(rows).join('\n');

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `power_data_export_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success notification
    showNotification('Data exported successfully as CSV', 'success');
}

/**
 * Export data as JSON file
 * @param {Array} data - The data to export
 * @param {boolean} includeAllParameters - Whether to include all parameters
 */
function exportAsJSON(data, includeAllParameters) {
    if (!data || !Array.isArray(data)) {
        showNotification('No data to export', 'warning');
        return;
    }

    let exportData;

    if (includeAllParameters) {
        // Export all data as is
        exportData = data;
    } else {
        // Only include essential parameters with calculated values
        exportData = data.map(item => {
            const voltageAvg = ((parseFloat(item.voltage_1) + parseFloat(item.voltage_2) + parseFloat(item.voltage_3)) / 3).toFixed(3);
            const currentTotal = (parseFloat(item.current_1) + parseFloat(item.current_2) + parseFloat(item.current_3)).toFixed(3);
            const pfAvg = ((parseFloat(item.pf_1) + parseFloat(item.pf_2) + parseFloat(item.pf_3)) / 3).toFixed(3);

            return {
                timestamp: item.timestamp,
                voltage_avg: parseFloat(voltageAvg),
                current_total: parseFloat(currentTotal),
                pf_avg: parseFloat(pfAvg),
                total_kw: parseFloat(item.total_kw),
                total_kva: parseFloat(item.total_kva),
                frequency: parseFloat(item.frequency)
            };
        });
    }

    // Create JSON content
    const jsonContent = JSON.stringify(exportData, null, 2);

    // Create download link
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `power_data_export_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success notification
    showNotification('Data exported successfully as JSON', 'success');
}

/**
 * Show a notification message
 * @param {string} message - The message to display
 * @param {string} type - The type of notification (info, success, warning, error)
 */
function showNotification(message, type = 'info') {
    const container = document.getElementById('notificationContainer');
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <span class="notification-message">${message}</span>
        <button class="notification-close">
            <span class="material-icons-round">close</span>
        </button>
    `;

    // Add to container
    container.appendChild(notification);

    // Add close button functionality
    notification.querySelector('.notification-close').addEventListener('click', function() {
        container.removeChild(notification);
    });

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode === container) {
            container.removeChild(notification);
        }
    }, 5000);
}

/**
 * Update status indicator
 * @param {string} message - The status message
 * @param {string} type - The status type (info, success, warning, error)
 */
function updateStatus(message, type = 'info') {
    const statusIndicator = document.getElementById('statusIndicator');

    // Only show status for warnings and errors
    if (type === 'warning' || type === 'error') {
        statusIndicator.className = `status ${type}`;
        statusIndicator.textContent = message;
        statusIndicator.style.display = 'block';

        // Auto-hide after 5 seconds
        setTimeout(() => {
            statusIndicator.style.display = 'none';
        }, 5000);
    } else {
        // For info and success, just log to console
        console.log(`Status (${type}): ${message}`);
    }
}

/**
 * Toggle pause state for a chart
 * @param {string} chartType - The type of chart to toggle
 */
function togglePause(chartType) {
    let chart, pauseButton;

    // Get the chart and pause button based on type
    switch (chartType) {
        case 'voltage':
            chart = voltageChart;
            pauseButton = document.getElementById('voltagePause');
            break;
        case 'current':
            chart = currentChart;
            pauseButton = document.getElementById('currentPause');
            break;
        case 'pf':
            chart = pfChart;
            pauseButton = document.getElementById('pfPause');
            break;
        case 'kva':
            chart = kvaChart;
            pauseButton = document.getElementById('kvaPause');
            break;
        case 'totalPower':
            chart = totalPowerChart;
            pauseButton = document.getElementById('totalPowerPause');
            break;
        case 'frequency':
            chart = frequencyChart;
            pauseButton = document.getElementById('frequencyPause');
            break;
        default:
            return;
    }

    if (!chart || !pauseButton) return;

    // Toggle pause state
    const isPaused = pauseButton.getAttribute('data-paused') === 'true';
    pauseButton.setAttribute('data-paused', !isPaused);

    // Update button icon
    const icon = pauseButton.querySelector('.material-icons-round');
    if (icon) {
        icon.textContent = !isPaused ? 'play_arrow' : 'pause';
    }

    // Update chart pause state
    chart._paused = !isPaused;

    // If pausing, save current view range
    if (!isPaused) {
        chart._pausedRange = {
            min: chart.options.scales.x.min,
            max: chart.options.scales.x.max
        };
    }

    // Show notification
    showNotification(`Chart ${!isPaused ? 'paused' : 'resumed'}`, 'info');
}

/**
 * Toggle auto-scroll for a chart
 * @param {string} chartType - The type of chart to toggle
 */
function toggleAutoScroll(chartType) {
    let chart, autoScrollButton;

    // Get the chart and auto-scroll button based on type
    switch (chartType) {
        case 'voltage':
            chart = voltageChart;
            autoScrollButton = document.getElementById('voltageAutoScroll');
            break;
        case 'current':
            chart = currentChart;
            autoScrollButton = document.getElementById('currentAutoScroll');
            break;
        case 'pf':
            chart = pfChart;
            autoScrollButton = document.getElementById('pfAutoScroll');
            break;
        case 'kva':
            chart = kvaChart;
            autoScrollButton = document.getElementById('kvaAutoScroll');
            break;
        case 'totalPower':
            chart = totalPowerChart;
            autoScrollButton = document.getElementById('totalPowerAutoScroll');
            break;
        case 'frequency':
            chart = frequencyChart;
            autoScrollButton = document.getElementById('frequencyAutoScroll');
            break;
        default:
            return;
    }

    if (!chart || !autoScrollButton) return;

    // Toggle auto-scroll state
    const isAutoScroll = autoScrollButton.getAttribute('data-auto-scroll') === 'true';
    autoScrollButton.setAttribute('data-auto-scroll', !isAutoScroll);

    // Update chart auto-scroll state
    chart._autoScroll = !isAutoScroll;

    // Show notification
    showNotification(`Auto-scroll ${!isAutoScroll ? 'disabled' : 'enabled'}`, 'info');
}

/**
 * Toggle fullscreen mode for a widget
 * @param {string} widgetId - The ID of the widget to expand
 */
function toggleWidgetExpand(widgetId) {
    const widget = document.getElementById(widgetId);
    const fullscreenWidget = document.getElementById('fullscreenWidget');
    const fullscreenContent = document.getElementById('fullscreenContent');
    const fullscreenTitle = document.getElementById('fullscreenTitle');
    const fullscreenIcon = document.getElementById('fullscreenIcon');

    if (!widget || !fullscreenWidget || !fullscreenContent) return;

    // Get widget title and icon
    const widgetTitle = widget.querySelector('.widget-title h3').textContent;
    const widgetIcon = widget.querySelector('.widget-title .material-icons-round').textContent;

    // Set fullscreen title and icon
    fullscreenTitle.textContent = widgetTitle;
    fullscreenIcon.textContent = widgetIcon;

    // Get chart canvas
    const chartCanvas = widget.querySelector('canvas');
    const chartId = chartCanvas.id;

    // Create a clone of the chart for fullscreen
    const fullscreenCanvas = document.createElement('canvas');
    fullscreenCanvas.id = 'fullscreen' + chartId;
    fullscreenContent.innerHTML = '';
    fullscreenContent.appendChild(fullscreenCanvas);

    // Add instant values display
    const instantValues = document.createElement('div');
    instantValues.className = 'instant-values';
    instantValues.id = 'fullscreen' + widget.querySelector('.instant-values').id;
    fullscreenContent.appendChild(instantValues);

    // Show fullscreen widget
    fullscreenWidget.classList.add('active');

    // Clone the chart
    let chart;
    switch (chartId) {
        case 'voltageChart':
            chart = voltageChart;
            break;
        case 'currentChart':
            chart = currentChart;
            break;
        case 'pfChart':
            chart = pfChart;
            break;
        case 'kvaChart':
            chart = kvaChart;
            break;
        case 'totalPowerChart':
            chart = totalPowerChart;
            break;
        case 'frequencyChart':
            chart = frequencyChart;
            break;
        default:
            return;
    }

    // Create a new chart with the same data
    const fullscreenChart = new Chart(fullscreenCanvas.getContext('2d'), {
        type: 'line',
        data: chart.data,
        options: chart.options,
        plugins: chart.plugins
    });

    // Store reference to the fullscreen chart
    fullscreenWidget._chart = fullscreenChart;
    fullscreenWidget._sourceChartId = chartId;

    // Copy instant values
    instantValues.innerHTML = widget.querySelector('.instant-values').innerHTML;
}

/**
 * Close fullscreen widget
 */
function closeFullscreen() {
    const fullscreenWidget = document.getElementById('fullscreenWidget');

    if (!fullscreenWidget) return;

    // Destroy the fullscreen chart
    if (fullscreenWidget._chart) {
        fullscreenWidget._chart.destroy();
        fullscreenWidget._chart = null;
    }

    // Hide fullscreen widget
    fullscreenWidget.classList.remove('active');
}

/**
 * Navigate to previous hour
 */
function navigateToPreviousHour() {
    // Get current time range from voltage chart
    const currentMin = voltageChart.options.scales.x.min;
    const currentMax = voltageChart.options.scales.x.max;

    if (!currentMin || !currentMax) return;

    // Calculate new time range (1 hour back)
    const timeWindow = currentMax - currentMin;
    const newMax = new Date(currentMin.getTime());
    const newMin = new Date(newMax.getTime() - timeWindow);

    // Update time display
    document.getElementById('timeDisplay').textContent = formatTimeRange(newMin, newMax);

    // Pause all charts
    pauseAllCharts();

    // Load historical data for the new time range
    loadHistoricalData(newMin, newMax);
}

/**
 * Navigate to next hour
 */
function navigateToNextHour() {
    // Get current time range from voltage chart
    const currentMin = voltageChart.options.scales.x.min;
    const currentMax = voltageChart.options.scales.x.max;

    if (!currentMin || !currentMax) return;

    // Calculate new time range (1 hour forward)
    const timeWindow = currentMax - currentMin;
    const newMin = new Date(currentMax.getTime());
    const newMax = new Date(newMin.getTime() + timeWindow);

    // Check if we're trying to go beyond current time
    const now = new Date();
    if (newMax > now) {
        // If so, set to current time and resume live updates
        resumeAllCharts();
        document.getElementById('timeDisplay').textContent = 'Current';
        return;
    }

    // Update time display
    document.getElementById('timeDisplay').textContent = formatTimeRange(newMin, newMax);

    // Pause all charts
    pauseAllCharts();

    // Load historical data for the new time range
    loadHistoricalData(newMin, newMax);
}

/**
 * Format time range for display
 * @param {Date} startTime - The start time
 * @param {Date} endTime - The end time
 * @returns {string} Formatted time range
 */
function formatTimeRange(startTime, endTime) {
    const options = {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    };

    return `${startTime.toLocaleTimeString('en-IN', options)} - ${endTime.toLocaleTimeString('en-IN', options)}`;
}

/**
 * Pause all charts
 */
function pauseAllCharts() {
    isPaused = true;

    // Pause each chart
    const chartTypes = ['voltage', 'current', 'pf', 'kva', 'totalPower', 'frequency'];
    chartTypes.forEach(type => {
        const pauseButton = document.getElementById(`${type}Pause`);
        if (pauseButton && pauseButton.getAttribute('data-paused') !== 'true') {
            togglePause(type);
        }
    });
}

/**
 * Resume all charts
 */
function resumeAllCharts() {
    isPaused = false;

    // Resume each chart
    const chartTypes = ['voltage', 'current', 'pf', 'kva', 'totalPower', 'frequency'];
    chartTypes.forEach(type => {
        const pauseButton = document.getElementById(`${type}Pause`);
        if (pauseButton && pauseButton.getAttribute('data-paused') === 'true') {
            togglePause(type);
        }
    });

    // Fetch latest data immediately
    fetchDataAndUpdateCharts();
}

/**
 * Load historical data for a specific time range
 * @param {Date} startTime - The start time
 * @param {Date} endTime - The end time
 */
async function loadHistoricalData(startTime, endTime) {
    try {
        // Show loading notification
        showNotification('Loading historical data...', 'info');

        // Format dates for API
        const startTimeStr = startTime.toISOString();
        const endTimeStr = endTime.toISOString();

        // Fetch historical data
        const response = await fetch(`../backend/get_historical_data.php?start=${startTimeStr}&end=${endTimeStr}&limit=1000`);

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();

        if (data.error) {
            throw new Error(data.error);
        }

        // Clear existing chart data
        clearAllChartData();

        // Add historical data to charts
        if (Array.isArray(data)) {
            data.forEach(point => {
                updateChartsWithData(point);
            });

            // Set time range for all charts
            const charts = [voltageChart, currentChart, pfChart, kvaChart, totalPowerChart, frequencyChart];
            charts.forEach(chart => {
                if (!chart) return;

                chart.options.scales.x.min = startTime;
                chart.options.scales.x.max = endTime;
                chart.update();
            });

            showNotification('Historical data loaded successfully', 'success');
        } else {
            showNotification('No historical data found for the selected time range', 'warning');
        }
    } catch (error) {
        console.error('Error loading historical data:', error);
        showNotification('Failed to load historical data: ' + error.message, 'error');
    }
}

/**
 * Clear data from all charts
 */
function clearAllChartData() {
    clearChartData(voltageChart);
    clearChartData(currentChart);
    clearChartData(pfChart);
    clearChartData(kvaChart);
    clearChartData(totalPowerChart);
    clearChartData(frequencyChart);
}

/**
 * Create a professional chart with Chart.js
 * @param {string} canvasId - The canvas element ID
 * @param {Array} labels - The data labels
 * @param {Array} colors - The color scheme
 * @param {string} yAxisLabel - The Y-axis label
 * @param {Object} options - Additional options
 * @param {string} type - The chart type
 * @returns {Chart} The created chart instance
 */
function createProfessionalChart(canvasId, labels, colors, yAxisLabel, options = {}, type = '') {
    const ctx = document.getElementById(canvasId);
    if (!ctx) {
        console.error(`Canvas element with ID '${canvasId}' not found`);
        return null;
    }

    const datasets = labels.map((label, index) => ({
        label: label,
        data: [],
        borderColor: colors[index % colors.length],
        backgroundColor: colors[index % colors.length] + '20',
        borderWidth: dashboardSettings.lineThickness || 0.5,
        fill: false,
        tension: 0.1,
        pointRadius: 0,
        pointHoverRadius: 4,
        pointBackgroundColor: colors[index % colors.length],
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2
    }));

    const chartConfig = {
        type: 'line',
        data: {
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        color: darkMode ? 'rgba(255, 255, 255, 0.8)' : '#495057'
                    }
                },
                tooltip: {
                    backgroundColor: darkMode ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.9)',
                    titleColor: darkMode ? '#ffffff' : '#202124',
                    bodyColor: darkMode ? 'rgba(255, 255, 255, 0.8)' : '#5f6368',
                    borderColor: darkMode ? 'rgba(255, 255, 255, 0.2)' : '#dadce0',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        title: function(context) {
                            const date = new Date(context[0].parsed.x);
                            return date.toLocaleTimeString('en-IN', {
                                timeZone: 'Asia/Kolkata',
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit'
                            });
                        },
                        label: function(context) {
                            const value = parseFloat(context.parsed.y).toFixed(dashboardSettings.decimalPlaces);
                            return `${context.dataset.label}: ${value}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    type: 'time',
                    time: {
                        displayFormats: {
                            second: 'HH:mm:ss',
                            minute: 'HH:mm',
                            hour: 'HH:mm'
                        }
                    },
                    grid: {
                        display: dashboardSettings.showGridLines,
                        color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.03)'
                    },
                    ticks: {
                        color: darkMode ? 'rgba(255, 255, 255, 0.7)' : '#495057',
                        maxTicksLimit: 8
                    }
                },
                y: {
                    grid: {
                        display: dashboardSettings.showGridLines,
                        color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.03)'
                    },
                    ticks: {
                        color: darkMode ? 'rgba(255, 255, 255, 0.7)' : '#495057',
                        callback: function(value) {
                            return parseFloat(value).toFixed(1);
                        }
                    },
                    title: {
                        display: true,
                        text: yAxisLabel,
                        color: darkMode ? 'rgba(255, 255, 255, 0.8)' : '#495057'
                    }
                }
            },
            animation: {
                duration: 0
            },
            ...options
        }
    };

    return new Chart(ctx, chartConfig);
}

/**
 * Update a professional chart with new data
 * @param {Chart} chart - The chart instance
 * @param {Date} timestamp - The timestamp for the data point
 * @param {Array} values - The values to add
 * @param {boolean} autoScroll - Whether to auto-scroll the chart
 */
function updateProfessionalChart(chart, timestamp, values, autoScroll = true) {
    if (!chart || !timestamp || !Array.isArray(values)) {
        console.warn('Invalid chart update parameters:', { chart: !!chart, timestamp, values });
        return;
    }

    // Debug: Log chart update
    console.log(`Updating chart with values:`, values, 'at time:', timestamp.toLocaleTimeString());

    // Add data to each dataset - ALWAYS add values, even if zero
    values.forEach((value, index) => {
        if (chart.data.datasets[index]) {
            // Ensure value is a valid number (including 0)
            const numericValue = typeof value === 'number' ? value : parseFloat(value) || 0;

            const dataPoint = {
                x: timestamp,
                y: numericValue
            };

            chart.data.datasets[index].data.push(dataPoint);

            // Debug: Log data point addition
            console.log(`Added data point to dataset ${index}:`, dataPoint);

            // Keep only the last N data points based on time window
            const maxPoints = dashboardSettings.timeWindow * 60; // 1 point per second
            if (chart.data.datasets[index].data.length > maxPoints) {
                const removed = chart.data.datasets[index].data.shift();
                console.log(`Removed old data point:`, removed);
            }
        } else {
            console.warn(`Dataset ${index} not found in chart`);
        }
    });

    // Auto-scroll to show latest data
    if (autoScroll && !isPaused) {
        const now = new Date();
        const timeWindow = dashboardSettings.timeWindow * 60 * 1000; // Convert to milliseconds
        chart.options.scales.x.min = new Date(now.getTime() - timeWindow);
        chart.options.scales.x.max = now;
    }

    // Force chart update with animation disabled for better performance
    chart.update('none');

    // Debug: Log chart data length
    console.log(`Chart now has ${chart.data.datasets[0]?.data.length || 0} data points`);
}

/**
 * Scroll chart backward in time
 * @param {string} chartType - The type of chart to scroll
 */
function scrollChartBackward(chartType) {
    const chart = getChartByType(chartType);
    if (!chart) return;

    const currentMin = chart.options.scales.x.min || new Date();
    const currentMax = chart.options.scales.x.max || new Date();
    const timeWindow = currentMax - currentMin;
    const scrollAmount = timeWindow * 0.1; // Scroll by 10% of the current window

    chart.options.scales.x.min = new Date(currentMin.getTime() - scrollAmount);
    chart.options.scales.x.max = new Date(currentMax.getTime() - scrollAmount);
    chart.update();
}

/**
 * Scroll chart forward in time
 * @param {string} chartType - The type of chart to scroll
 */
function scrollChartForward(chartType) {
    const chart = getChartByType(chartType);
    if (!chart) return;

    const currentMin = chart.options.scales.x.min || new Date();
    const currentMax = chart.options.scales.x.max || new Date();
    const timeWindow = currentMax - currentMin;
    const scrollAmount = timeWindow * 0.1; // Scroll by 10% of the current window

    chart.options.scales.x.min = new Date(currentMin.getTime() + scrollAmount);
    chart.options.scales.x.max = new Date(currentMax.getTime() + scrollAmount);
    chart.update();
}

/**
 * Get chart instance by type
 * @param {string} chartType - The chart type
 * @returns {Chart|null} The chart instance
 */
function getChartByType(chartType) {
    switch (chartType) {
        case 'voltage': return voltageChart;
        case 'current': return currentChart;
        case 'pf': return pfChart;
        case 'kva': return kvaChart;
        case 'totalPower': return totalPowerChart;
        case 'frequency': return frequencyChart;
        default: return null;
    }
}

// Make functions available globally
window.togglePause = togglePause;
window.toggleAutoScroll = toggleAutoScroll;
window.toggleWidgetExpand = toggleWidgetExpand;
window.closeFullscreen = closeFullscreen;
window.scrollChartBackward = scrollChartBackward;
window.scrollChartForward = scrollChartForward;
