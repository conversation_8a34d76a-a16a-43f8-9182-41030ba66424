@import 'variables';

.history-container {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-xl;
  box-shadow: $futuristic-shadow;
  padding: 25px;
  margin-bottom: 25px;
  border: 1px solid $futuristic-border;
  transition: $transition-normal;
  animation: fade-in-up 0.8s ease forwards;
  animation-delay: 0.4s;
  opacity: 1; // Make visible by default, animation will enhance
}

.history-container:hover {
  box-shadow: $futuristic-glow;
}

.filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-lg;
  border: 1px solid $futuristic-border;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  margin-bottom: 8px;
  font-weight: 500;
  font-size: $font-size-sm;
  color: $futuristic-text-secondary;
  letter-spacing: 0.5px;
}

.filter-actions {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

input, select {
  padding: 12px 15px;
  border: 1px solid $futuristic-border;
  border-radius: $border-radius-md;
  background: rgba(255, 255, 255, 0.9);
  color: $futuristic-text;
  font-size: $font-size-sm;
  transition: $transition-normal;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

input:focus, select:focus {
  border-color: $futuristic-accent-1;
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.3);
  outline: none;
}

.button {
  padding: 12px 18px;
  border: none;
  border-radius: $border-radius-md;
  background: $futuristic-gradient;
  color: white;
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: $transition-normal;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: $transition-slow;
}

.button:hover {
  background: $futuristic-gradient-hover;
  transform: translateY(-2px);
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.button:hover::before {
  left: 100%;
}

.button-primary {
  background: linear-gradient(135deg, #06b6d4, #0ea5e9);
}

.button-primary:hover {
  background: linear-gradient(135deg, #0891b2, #0284c7);
}

.button-secondary {
  background: linear-gradient(135deg, #475569, #64748b);
}

.button-secondary:hover {
  background: linear-gradient(135deg, #334155, #475569);
}

.button:disabled {
  background: #475569;
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.button:disabled::before {
  display: none;
}

.data-table-container {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-lg;
  padding: 20px;
  margin-bottom: 20px;
  overflow: auto;
  box-shadow: $futuristic-shadow;
  border: 1px solid $futuristic-border;
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 20px;
}

th, td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

th {
  background: rgba(255, 255, 255, 0.95);
  font-weight: 500;
  color: $futuristic-text;
  position: sticky;
  top: 0;
  z-index: $z-index-sticky;
  backdrop-filter: blur(10px);
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: $font-size-xs;
}

tr {
  transition: $transition-normal;
}

tr:hover {
  background: rgba(59, 130, 246, 0.1);
}

td {
  font-family: 'JetBrains Mono', monospace;
  font-size: $font-size-sm;
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.pagination-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.page-info {
  font-size: $font-size-sm;
  color: $futuristic-text-secondary;
  font-family: 'JetBrains Mono', monospace;
}

.message {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: $border-radius-lg;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-left: 4px solid $futuristic-border;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message.error {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: #ef4444;
  color: #fca5a5;
}

.message.success {
  background: rgba(16, 185, 129, 0.1);
  border-left-color: #10b981;
  color: #6ee7b7;
}

.message.warning {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: #f59e0b;
  color: #fcd34d;
}

.message.info {
  background: rgba(59, 130, 246, 0.1);
  border-left-color: #3b82f6;
  color: #93c5fd;
}

.loading {
  text-align: center;
  padding: 20px;
  color: $futuristic-text-secondary;
}

.loading::after {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(6, 182, 212, 0.3);
  border-radius: 50%;
  border-top-color: $futuristic-accent-1;
  animation: spin 1s ease-in-out infinite;
  margin-left: 10px;
  vertical-align: middle;
}

.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255,255,255,0.1);
  border-radius: 50%;
  border-top-color: $futuristic-accent-1;
  animation: spin 1s ease-in-out infinite;
  box-shadow: 0 0 10px rgba(6, 182, 212, 0.5);
}

.quick-filters {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.quick-filter {
  padding: 10px 15px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.8);
  color: $futuristic-text;
  font-size: $font-size-sm;
  cursor: pointer;
  transition: $transition-normal;
  border: 1px solid $futuristic-border;
  backdrop-filter: blur(10px);
}

.quick-filter:hover {
  background: rgba(14, 165, 233, 0.15);
  border-color: $futuristic-accent-1;
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.quick-filter.active {
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.2), rgba(59, 130, 246, 0.2));
  border-color: $futuristic-accent-1;
  color: $futuristic-accent-1;
  box-shadow: 0 0 10px rgba(14, 165, 233, 0.3);
}

.data-visualization {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-lg;
  padding: 20px;
  margin-bottom: 25px;
  height: 350px;
  box-shadow: $futuristic-shadow;
  border: 1px solid $futuristic-border;
  transition: $transition-normal;
}

.data-visualization:hover {
  box-shadow: $futuristic-glow;
}

.visualization-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 15px;
}

.visualization-title {
  font-size: $font-size-md;
  font-weight: 500;
  color: $futuristic-text;
  letter-spacing: 0.5px;
}

.parameter-selection {
  display: flex;
  align-items: center;
  gap: 10px;
}

.parameter-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.parameter-btn {
  padding: 8px 12px;
  border: 1px solid $futuristic-border;
  border-radius: $border-radius-md;
  background: rgba(255, 255, 255, 0.8);
  color: $futuristic-text-secondary;
  cursor: pointer;
  transition: $transition-normal;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: $font-size-xs;
  font-weight: 500;
  letter-spacing: 0.5px;
  backdrop-filter: blur(10px);
}

.parameter-btn:hover {
  background: rgba(14, 165, 233, 0.15);
  border-color: $futuristic-accent-1;
  color: $futuristic-accent-1;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.parameter-btn.active {
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.2), rgba(59, 130, 246, 0.2));
  border-color: $futuristic-accent-1;
  color: $futuristic-accent-1;
  box-shadow: 0 0 10px rgba(14, 165, 233, 0.3);
}

.parameter-btn .material-icons-round {
  font-size: 16px;
}

.visualization-actions {
  display: flex;
  gap: 10px;
}

.action-button {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid $futuristic-border;
  border-radius: $border-radius-md;
  padding: 8px;
  color: $futuristic-text;
  cursor: pointer;
  transition: $transition-normal;
}

.action-button:hover {
  background: rgba(14, 165, 233, 0.15);
  border-color: $futuristic-accent-1;
  color: $futuristic-accent-1;
  transform: translateY(-2px);
}

@media (max-width: $breakpoint-md) {
  .filters {
    grid-template-columns: 1fr;
  }

  .filter-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .pagination {
    flex-direction: column;
    gap: 15px;
  }

  .visualization-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .parameter-group {
    justify-content: flex-start;
  }

  .parameter-btn {
    font-size: 10px;
    padding: 6px 10px;
  }

  .parameter-btn .material-icons-round {
    font-size: 14px;
  }
}
