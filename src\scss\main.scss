@import 'variables';

// Reset and base styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: $futuristic-bg;
  color: $futuristic-text;
  line-height: 1.5;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

// Animated background
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 50%, rgba(14, 165, 233, 0.05) 0%, rgba(248, 250, 252, 0) 50%);
  z-index: -1;
  animation: pulse-bg 15s infinite alternate;
}

@keyframes pulse-bg {
  0% { opacity: 0.5; }
  100% { opacity: 1; }
}

// Animated grid overlay
body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
      linear-gradient(rgba(14, 165, 233, 0.08) 1px, transparent 1px),
      linear-gradient(90deg, rgba(14, 165, 233, 0.08) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: -1;
  opacity: 0.4;
  animation: grid-move 120s linear infinite;
}

@keyframes grid-move {
  0% { background-position: 0 0; }
  100% { background-position: 50px 50px; }
}

// Dashboard container
.dashboard-container {
  display: flex;
  min-height: 100vh;
}

// Sidebar
.sidebar {
  width: 250px;
  background: linear-gradient(180deg, $futuristic-bg 0%, #e2e8f0 100%);
  border-right: 1px solid $futuristic-border;
  box-shadow: $futuristic-shadow;
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  z-index: $z-index-fixed;
  animation: fade-in-up 0.8s ease forwards;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid $futuristic-border;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav li {
  margin-bottom: 5px;
}

.sidebar-nav a {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: $futuristic-text-secondary;
  text-decoration: none;
  transition: $transition-normal;
  border-left: 3px solid transparent;
}

.sidebar-nav a:hover {
  background: rgba(14, 165, 233, 0.1);
  color: $futuristic-accent-1;
  border-left-color: $futuristic-accent-1;
}

.sidebar-nav li.active a {
  background: rgba(14, 165, 233, 0.15);
  color: $futuristic-accent-1;
  border-left-color: $futuristic-accent-1;
  box-shadow: inset 0 0 10px rgba(14, 165, 233, 0.1);
}

.sidebar-nav .material-icons-round {
  margin-right: 12px;
  font-size: 20px;
  color: $futuristic-accent-1;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid $futuristic-border;
}

.system-status {
  display: flex;
  align-items: center;
  margin: 15px 0;
  color: $futuristic-text-secondary;
  font-size: $font-size-sm;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.online {
  background: $futuristic-accent-1;
  box-shadow: 0 0 10px $futuristic-accent-1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.7); }
  70% { box-shadow: 0 0 0 5px rgba(14, 165, 233, 0); }
  100% { box-shadow: 0 0 0 0 rgba(14, 165, 233, 0); }
}

// Logout button styles removed - no login system

// Main content
.main-content {
  flex: 1;
  padding: 20px;
  margin-left: 250px;
  width: calc(100% - 250px);
  animation: fade-in-up 0.8s ease forwards;
  animation-delay: 0.3s;
  opacity: 1; // Make visible by default, animation will enhance
}

// Status header
.status-header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-xl;
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: $futuristic-shadow;
  border: 1px solid $futuristic-border;
}

.status-title {
  display: flex;
  align-items: center;
}

.header-logo-container {
  margin-right: 15px;
}

.header-logo {
  height: 40px;
}

.subtitle {
  font-size: $font-size-lg;
  color: $futuristic-text-secondary;
  font-weight: 400;
}

.status-boxes {
  display: flex;
  gap: 15px;
}

.status-box {
  background: rgba(255, 255, 255, 0.9);
  border-radius: $border-radius-lg;
  padding: 15px;
  display: flex;
  align-items: center;
  min-width: 180px;
  border: 1px solid $futuristic-border;
  transition: $transition-normal;
  position: relative;
}

.status-box:hover {
  box-shadow: $futuristic-glow;
  transform: translateY(-2px);
}

.status-icon {
  background: rgba(14, 165, 233, 0.15);
  color: $futuristic-accent-1;
  width: 40px;
  height: 40px;
  border-radius: $border-radius-lg;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.status-icon .material-icons-round {
  font-size: 24px;
}

.status-text {
  flex: 1;
}

.status-label {
  font-size: $font-size-xs;
  color: $futuristic-text-secondary;
  margin-bottom: 2px;
}

.status-value {
  font-size: $font-size-md;
  font-weight: 600;
  color: $futuristic-text;
}

.status-box.system .status-icon {
  background: rgba(14, 165, 233, 0.15);
  color: $futuristic-accent-1;
}

// Fade in animations
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive adjustments
@media (max-width: $breakpoint-lg) {
  .sidebar {
    width: 70px;
    overflow: hidden;
  }
  
  .sidebar-header {
    padding: 15px 10px;
  }
  
  .sidebar-nav a span:not(.material-icons-round) {
    display: none;
  }
  
  .sidebar-nav .material-icons-round {
    margin-right: 0;
  }
  
  .sidebar-footer {
    padding: 15px 10px;
  }
  
  .system-status span:not(.status-indicator) {
    display: none;
  }
  
  .main-content {
    margin-left: 70px;
    width: calc(100% - 70px);
  }
}

@media (max-width: $breakpoint-md) {
  .status-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .status-boxes {
    margin-top: 15px;
    width: 100%;
    flex-wrap: wrap;
  }
  
  .status-box {
    flex: 1;
    min-width: 140px;
  }
}

@media (max-width: $breakpoint-sm) {
  .main-content {
    padding: 15px;
  }
  
  .status-boxes {
    flex-direction: column;
  }
  
  .status-box {
    width: 100%;
  }
}
