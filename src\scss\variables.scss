// Light mode color palette
$futuristic-bg: #f8fafc;
$futuristic-card-bg: rgba(255, 255, 255, 0.9);
$futuristic-accent-1: #0ea5e9;
$futuristic-accent-2: #3b82f6;
$futuristic-accent-3: #8b5cf6;
$futuristic-text: #1e293b;
$futuristic-text-secondary: #64748b;
$futuristic-border: rgba(148, 163, 184, 0.3);
$futuristic-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
$futuristic-glow: 0 0 15px rgba(14, 165, 233, 0.3);
$futuristic-gradient: linear-gradient(135deg, #0ea5e9, #3b82f6, #8b5cf6);
$futuristic-gradient-hover: linear-gradient(135deg, #0284c7, #2563eb, #7c3aed);

// Breakpoints
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

// Spacing
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// Font sizes
$font-size-xs: 0.75rem;
$font-size-sm: 0.875rem;
$font-size-md: 1rem;
$font-size-lg: 1.25rem;
$font-size-xl: 1.5rem;

// Border radius
$border-radius-sm: 6px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;
$border-radius-pill: 9999px;

// Transitions
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// Z-index
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
